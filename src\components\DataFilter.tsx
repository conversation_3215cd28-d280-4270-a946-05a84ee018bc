import React from 'react';

interface FilterCriteria {
  zone?: string;
  location?: string;
  dealer?: string;
  areaOffice?: string;
  category?: string;
}

interface DataFilterProps {
  rawData: any[];
  filters: FilterCriteria;
}

// Helper function to get zone name from zone code
function getZoneName(zoneCode: string): string {
  const zoneMap: Record<string, string> = {
    'N': 'North',
    'S': 'South',
    'E': 'East',
    'W': 'West',
    'C': 'Central'
  };
  return zoneMap[zoneCode] || zoneCode;
}

// Helper function to get category name from category code
function getCategoryName(categoryCode: string): string {
  const categoryMap: Record<string, string> = {
    'A': 'Category A',
    'B': 'Category B',
    'C': 'Category C',
    'D': 'Category D'
  };
  return categoryMap[categoryCode] || categoryCode;
}

// Get vendor details from API data
const getVendorDetails = (dataArray: any[], vendorCode: string, dealerId: number) => {
  // Find the data object that matches the vendorCode and dealerId
  const matchingData = dataArray.find(dataObject =>
    dataObject.vendorCode === vendorCode && dataObject.dealerId === dealerId
  );

  if (matchingData && matchingData.vendor) {
    return {
      zone: getZoneName(matchingData.vendor.dealerZone),
      city: matchingData.vendor.dealerLocation,
      dealerName: matchingData.vendor.dealerName,
      dealerCode: `${matchingData.vendor.dealerName.substring(0, 3).toUpperCase()}-${String(dealerId).padStart(3, '0')}`,
      areaOffice: matchingData.vendor.dealerAO,
      category: getCategoryName(matchingData.vendor.dealerCategory)
    };
  }

  // Fallback if no matching data found
  return {
    zone: 'Unknown Zone',
    city: 'Unknown Location',
    dealerName: `Dealer ${dealerId}`,
    dealerCode: `DLR-${String(dealerId).padStart(3, '0')}`,
    areaOffice: 'Unknown Area Office',
    category: 'Unknown Category'
  };
};

/**
 * DataFilter hook that filters raw API data based on provided criteria
 * @param rawData - Raw API data array
 * @param filters - Filter criteria object
 * @returns Filtered data array
 */
export const useDataFilter = ({ rawData, filters }: DataFilterProps): any[] => {
  if (!rawData || rawData.length === 0) {
    return [];
  }

  return rawData.filter(dataObject => {
    // Get vendor details using the helper function
    const vendorDetails = getVendorDetails(rawData, dataObject.vendorCode, dataObject.dealerId);

    // Apply zone filter
    if (filters.zone && filters.zone !== 'All') {
      if (vendorDetails.zone !== filters.zone) {
        return false;
      }
    }

    // Apply location filter
    if (filters.location && filters.location !== 'All') {
      if (vendorDetails.city !== filters.location) {
        return false;
      }
    }

    // Apply dealer filter
    if (filters.dealer && filters.dealer !== 'All') {
      if (vendorDetails.dealerName !== filters.dealer) {
        return false;
      }
    }

    // Apply area office filter
    if (filters.areaOffice && filters.areaOffice !== 'All') {
      if (vendorDetails.areaOffice !== filters.areaOffice) {
        return false;
      }
    }

    // Apply category filter
    if (filters.category && filters.category !== 'All') {
      if (vendorDetails.category !== filters.category) {
        return false;
      }
    }

    return true;
  });
};

export type { FilterCriteria, DataFilterProps };
export default useDataFilter;
