import React from 'react';

interface FilterCriteria {
  zone?: string;
  location?: string;
  dealer?: string;
  areaOffice?: string;
  category?: string;
}

interface DataFilterProps {
  rawData: any[];
  filters: FilterCriteria;
}

// API endpoint for fetching dealer checklist submissions
const API_ENDPOINT = 'https://api.eisqr.com/dealer-checklist-submissions/latest-by-vendor';

// Function to fetch data from API - exported for use in MSIHeatmap
export async function fetchPreApiData() {
  try {
    const response = await fetch(API_ENDPOINT);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();

    // Parse the stringified response field for each object
    return data.map((item: any) => ({
      ...item,
      response: typeof item.response === 'string' ? JSON.parse(item.response) : item.response
    }));
  } catch (error) {
    console.error('Error fetching preAPI data:', error);
    throw error;
  }
}

// Mapping data for categories and zones
const categoryList = [
  { name: 'Forging & Machining', value: 1 },
  { name: 'Casting & Machining', value: 2 },
  { name: 'Pressing & Fabrication', value: 3 },
  { name: 'Proprietary Mechanical', value: 4 },
  { name: 'Proprietary Electrical', value: 5 },
  { name: 'Plastics, Rubber, Painting and Stickers', value: 6 },
  { name: 'EV/3W/2W', value: 7 },
  { name: 'BW', value: 8 },
  { name: 'Accessories', value: 9 }
];

const zonalOfficeList = [
  { name: "Central", value: 1 },
  { name: "East", value: 2 },
  { name: "North", value: 3 },
  { name: "South", value: 9 },
  { name: "South1", value: 4 },
  { name: "South2", value: 5 },
  { name: "West", value: 8 },
  { name: "West1", value: 6 },
  { name: "West2", value: 7 }
];

// Helper functions to map values to names
const getCategoryName = (value: number): string => {
  const category = categoryList.find(cat => cat.value === value);
  return category ? category.name : `Category ${value}`;
};

const getZoneName = (value: number): string => {
  const zone = zonalOfficeList.find(zone => zone.value === value);
  return zone ? zone.name : `Zone ${value}`;
};

// Get vendor details from API data - exported for use in other components
export const getVendorDetails = (dataArray: any[], vendorCode: string, dealerId: number) => {
  // Find the data object that matches the vendorCode and dealerId
  const matchingData = dataArray.find(dataObject =>
    dataObject.vendorCode === vendorCode && dataObject.dealerId === dealerId
  );

  console.log('Getting vendor details for:', { vendorCode, dealerId });
  console.log('Matching data found:', !!matchingData);
  console.log('Vendor object exists:', !!matchingData?.vendor);

  if (matchingData && matchingData.vendor) {
    const vendorDetails = {
      zone: getZoneName(matchingData.vendor.dealerZone),
      city: matchingData.vendor.dealerLocation,
      dealerName: matchingData.vendor.dealerName,
      dealerCode: `${matchingData.vendor.dealerName.substring(0, 3).toUpperCase()}-${String(dealerId).padStart(3, '0')}`,
      areaOffice: matchingData.vendor.dealerAO,
      category: getCategoryName(matchingData.vendor.dealerCategory)
    };

    console.log('Vendor details extracted:', vendorDetails);
    return vendorDetails;
  }

  // Fallback if no matching data found
  return {
    zone: 'Unknown Zone',
    city: 'Unknown Location',
    dealerName: `Dealer ${dealerId}`,
    dealerCode: `DLR-${String(dealerId).padStart(3, '0')}`,
    areaOffice: 'Unknown Area Office',
    category: 'Unknown Category'
  };
};

/**
 * DataFilter hook that filters raw API data based on provided criteria
 * @param rawData - Raw API data array
 * @param filters - Filter criteria object
 * @returns Filtered data array
 */
export const useDataFilter = ({ rawData, filters }: DataFilterProps): any[] => {
  if (!rawData || rawData.length === 0) {
    return [];
  }

  return rawData.filter(dataObject => {
    // Get vendor details using the helper function
    const vendorDetails = getVendorDetails(rawData, dataObject.vendorCode, dataObject.dealerId);

    // Apply zone filter
    if (filters.zone && filters.zone !== 'All') {
      if (vendorDetails.zone !== filters.zone) {
        return false;
      }
    }

    // Apply location filter
    if (filters.location && filters.location !== 'All') {
      if (vendorDetails.city !== filters.location) {
        return false;
      }
    }

    // Apply dealer filter
    if (filters.dealer && filters.dealer !== 'All') {
      if (vendorDetails.dealerName !== filters.dealer) {
        return false;
      }
    }

    // Apply area office filter
    if (filters.areaOffice && filters.areaOffice !== 'All') {
      if (vendorDetails.areaOffice !== filters.areaOffice) {
        return false;
      }
    }

    // Apply category filter
    if (filters.category && filters.category !== 'All') {
      if (vendorDetails.category !== filters.category) {
        return false;
      }
    }

    return true;
  });
};

// Function to extract filter options from API data
export async function getFilterOptions() {
  try {
    const dataArray = await fetchPreApiData();

    const zones = new Set<string>();
    const locations = new Set<string>();
    const dealers = new Set<string>();
    const areaOffices = new Set<string>();
    const categories = new Set<string>();

    console.log('Extracting filter options from API data:', dataArray.length, 'objects');
    console.log('First data object structure:', dataArray[0]);

    // Extract unique values from all objects in the array
    dataArray.forEach(dataObject => {
      // Get vendor details using the helper function that works with actual API structure
      const vendorDetails = getVendorDetails(dataArray, dataObject.vendorCode, dataObject.dealerId);

      // Extract zone name
      if (vendorDetails.zone) {
        zones.add(vendorDetails.zone);
      }

      // Extract location (city)
      if (vendorDetails.city) {
        locations.add(vendorDetails.city);
      }

      // Extract dealer name
      if (vendorDetails.dealerName) {
        dealers.add(vendorDetails.dealerName);
      }

      // Extract area office
      if (vendorDetails.areaOffice) {
        areaOffices.add(vendorDetails.areaOffice);
      }

      // Extract category
      if (vendorDetails.category) {
        categories.add(vendorDetails.category);
      }
    });

    const filterOptions = {
      zones: ['All', ...Array.from(zones).sort()],
      locations: ['All', ...Array.from(locations).sort()],
      dealers: ['All', ...Array.from(dealers).sort()],
      areaOffices: ['All', ...Array.from(areaOffices).sort()],
      categories: ['All', ...Array.from(categories).sort()]
    };

    console.log('Filter options extracted:', filterOptions);

    return {
      zones: ['All', ...Array.from(zones).sort()],
      locations: ['All', ...Array.from(locations).sort()],
      dealers: ['All', ...Array.from(dealers).sort()],
      areaOffices: ['All', ...Array.from(areaOffices).sort()],
      categories: ['All', ...Array.from(categories).sort()]
    };
  } catch (error) {
    console.error('Error extracting filter options:', error);
    return {
      zones: ['All'],
      locations: ['All'],
      dealers: ['All'],
      areaOffices: ['All'],
      categories: ['All']
    };
  }
}

// Function to get filtered options based on current selections
export async function getFilteredOptions(currentFilters: {
  zone?: string;
  location?: string;
  dealer?: string;
  category?: string;
}) {
  try {
    const dataArray = await fetchPreApiData();

    const locations = new Set<string>();
    const dealers = new Set<string>();
    const areaOffices = new Set<string>();
    const categories = new Set<string>();

    console.log('Filtering options with current filters:', currentFilters);

    // Filter data based on current selections
    dataArray.forEach(dataObject => {
      // Get vendor details using the helper function
      const vendorDetails = getVendorDetails(dataArray, dataObject.vendorCode, dataObject.dealerId);

      // Apply zone filter
      if (currentFilters.zone && currentFilters.zone !== 'All') {
        if (vendorDetails.zone !== currentFilters.zone) {
          return;
        }
      }

      // Apply location filter
      if (currentFilters.location && currentFilters.location !== 'All' &&
        vendorDetails.city !== currentFilters.location) {
        return;
      }

      // Apply dealer filter
      if (currentFilters.dealer && currentFilters.dealer !== 'All' &&
        vendorDetails.dealerName !== currentFilters.dealer) {
        return;
      }

      // Apply category filter
      if (currentFilters.category && currentFilters.category !== 'All' &&
        vendorDetails.category !== currentFilters.category) {
        return;
      }

      // Add to available options if passes all filters
      if (vendorDetails.city) locations.add(vendorDetails.city);
      if (vendorDetails.dealerName) dealers.add(vendorDetails.dealerName);
      if (vendorDetails.areaOffice) areaOffices.add(vendorDetails.areaOffice);
      if (vendorDetails.category) categories.add(vendorDetails.category);
    });

    const filteredOptions = {
      locations: ['All', ...Array.from(locations).sort()],
      dealers: ['All', ...Array.from(dealers).sort()],
      areaOffices: ['All', ...Array.from(areaOffices).sort()],
      categories: ['All', ...Array.from(categories).sort()]
    };

    console.log('Filtered options result:', filteredOptions);

    return filteredOptions;
  } catch (error) {
    console.error('Error getting filtered options:', error);
    return {
      locations: ['All'],
      dealers: ['All'],
      areaOffices: ['All'],
      categories: ['All']
    };
  }
}

export type { FilterCriteria, DataFilterProps };
export default useDataFilter;
